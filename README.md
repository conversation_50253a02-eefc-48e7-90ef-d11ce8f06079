# 🎮 Bloodmoney Game Portal - 静态游戏门户网站

一个高性能、SEO友好的静态游戏门户网站，采用静态站点生成(SSG)架构，支持13种精美主题切换，专为游戏网站开发者设计。基于现代Web技术栈构建，提供完整的游戏展示和管理解决方案。

## ✨ 核心特性

- 🚀 **静态站点生成 (SSG)** - 极致性能，SEO友好，零服务器依赖
- 🎨 **13种主题支持** - 精美主题，一键切换，满足不同风格需求
- 📱 **响应式设计** - 完美适配桌面、平板、手机，移动优先设计
- 🎮 **灵活游戏管理** - 基于JSON的游戏数据管理，支持动态内容块
- 🔍 **智能搜索功能** - 实时游戏搜索，键盘导航，移动端优化，模糊匹配算法
- 📊 **完整SEO优化** - 元数据、Open Graph、Twitter Cards、智能Sitemap生成
- ⚡ **零框架依赖** - 纯HTML/CSS/JS，轻量级，加载速度极快
- 🔄 **自动化部署** - 支持Cloudflare Pages、Vercel等平台自动部署
- 🎯 **多尺寸iframe支持** - 支持小/大/超大三种iframe尺寸，自适应布局
- 📋 **组件化开发** - 模块化组件系统，易于扩展和维护
- 🎪 **动态内容块** - 支持YouTube视频、文本段落、自定义HTML等
- 📄 **法律信息页面** - 内置About Us、Privacy Policy、DMCA等页面
- 🗺️ **智能Sitemap管理** - 自动生成分类sitemap，支持功能开关控制
- 🚫 **自定义404页面** - 美观的404错误页面，保持网站主题一致性
- 🎲 **游戏全屏控制** - 支持游戏全屏模式，移动端自动全屏优化
- 🔧 **功能开关系统** - 灵活的功能开关，可独立控制Popular/New Games功能

## 🏗️ 技术架构

### 核心架构：静态站点生成 (SSG)
- **构建工具**：Node.js + 自定义构建脚本 (build.js)
- **模板引擎**：基于占位符的模板替换系统，支持动态内容注入
- **样式系统**：CSS变量体系 + 13种主题切换机制
- **交互功能**：原生JavaScript ES6+，模块化设计
- **部署方式**：静态文件部署，支持CDN加速，零服务器依赖

### 技术栈详情
- **前端框架**：纯HTML5 + CSS3 + JavaScript ES6+（无第三方框架依赖）
- **构建环境**：Node.js ≥ 14.0.0
- **样式架构**：CSS变量体系，支持13种主题动态切换
- **字体系统**：Google Fonts (Fredoka)，优雅的游戏风格字体
- **图片优化**：WebP格式支持，懒加载，响应式图片
- **SEO优化**：完整元数据，Open Graph，Twitter Cards，结构化数据
- **搜索功能**：客户端JavaScript搜索，模糊匹配算法，键盘导航
- **响应式设计**：移动优先，Flexbox + CSS Grid布局

## 📁 项目结构

```
Bloodmoney-Game-Portal/
├── config.json              # 全局配置文件（网站设置、主题配置、功能开关）
├── games.json               # 游戏数据文件（游戏信息、内容块、SEO元数据）
├── build.js                 # 构建脚本（静态站点生成器）
├── package.json             # 项目配置（依赖、脚本、元信息）
├── .gitignore               # Git忽略文件（开发模式）
├── .gitignore_game          # Git忽略文件（部署模式，只上传dist目录）
├── PRD.md                   # 产品需求文档
├── TODOLIST.md              # 开发任务清单
├── BUILD_PLAN.md            # 构建计划文档
├── src/                     # 源文件目录
│   ├── templates/           # HTML模板系统
│   │   ├── _main-layout-template.html      # 主页布局模板
│   │   ├── game-detail-template.html       # 游戏详情页模板
│   │   ├── _list-layout-template.html      # 分类列表布局模板
│   │   ├── _list-page-template.html        # 分页列表模板
│   │   └── 404.html                        # 404错误页面模板
│   ├── components/          # 组件模板
│   │   ├── game-card.html   # 游戏卡片组件
│   │   └── feature-blocks.html # 功能块组件（YouTube、段落等）
│   ├── css/                 # 样式文件
│   │   ├── style.css        # 主样式文件（2400+行CSS变量体系）
│   │   └── themes/          # 主题样式目录（13种主题）
│   │       ├── theme-default.css     # 默认主题（蓝色系）
│   │       ├── theme-default-1.css   # 默认主题变体1
│   │       ├── theme-default-2.css   # 默认主题变体2（当前使用）
│   │       ├── theme-retro.css       # 复古主题
│   │       ├── theme-cyberpunk.css   # 赛博朋克主题
│   │       ├── theme-cyberpunk-1.css # 赛博朋克变体
│   │       ├── theme-forest.css      # 森林主题
│   │       ├── theme-forest-1.css    # 森林变体
│   │       ├── theme-ocean.css       # 海洋主题
│   │       ├── theme-ocean-1.css     # 海洋变体
│   │       ├── theme-battle.css      # 战斗主题
│   │       ├── theme-battle-1.css    # 战斗变体（暗夜熔岩）
│   │       └── theme-dark.css        # 暗黑主题
│   ├── js/                  # JavaScript文件
│   │   └── main.js          # 主要交互功能（600+行，包含搜索、移动菜单、游戏控制等）
│   ├── images/              # 图片资源目录
│   │   ├── common/          # 公共图片
│   │   │   ├── logo.png     # 网站Logo
│   │   │   ├── favicon.ico  # 网站图标
│   │   │   └── bloodmoney.png # 主游戏图片
│   │   ├── popular_games_image/  # 热门游戏缩略图目录
│   │   └── new_games_image/     # 新游戏缩略图目录
│   └── legal_info/          # 法律信息页面
│       ├── About-Us.html    # 关于我们页面
│       ├── Contact-Us.html  # 联系我们页面
│       ├── DMCA.html        # DMCA版权声明
│       └── Privacy-Policy.html # 隐私政策
└── dist/                    # 构建输出目录（自动生成）
    ├── index.html           # 主页
    ├── 404.html             # 自定义404错误页面
    ├── sitemap.xml          # 主sitemap索引文件
    ├── robots.txt           # 搜索引擎爬虫配置
    ├── _redirects           # Cloudflare Pages重定向配置
    ├── sitemaps/            # Sitemap文件目录
    │   ├── sitemap-static.xml    # 静态页面sitemap
    │   ├── sitemap-popular.xml   # 热门游戏sitemap
    │   ├── sitemap-new.xml       # 新游戏sitemap
    │   └── sitemap-legal.xml     # 法律页面sitemap
    ├── popular/             # 热门游戏分类页（可选）
    ├── new/                 # 新游戏分类页（可选）
    ├── popular_games/       # 热门游戏详情页（可选）
    ├── new_games/           # 新游戏详情页（可选）
    ├── css/                 # 样式文件（复制）
    ├── js/                  # JavaScript文件（复制+生成）
    │   ├── main.js          # 主要交互功能
    │   └── games-data.js    # 自动生成的搜索数据
    ├── images/              # 图片资源（完整复制）
    └── legal_info/          # 法律信息页面（复制）
```

## 🚀 快速开始

### 环境要求

- Node.js >= 14.0.0
- 无需其他依赖（零框架依赖）
- 支持Windows、macOS、Linux

### 安装和使用

1. **克隆或下载项目**
```bash
# 克隆项目到本地
git clone <repository-url> my-bloodmoney-site
cd my-bloodmoney-site
```

### 当前项目配置
本项目已经配置为 **Bloodmoney Game Portal**，包含：
- 网站名称：Bloodmoney
- 域名：bloodmoneygame.space
- 主题：default-2（当前使用）
- 主要游戏：Bloodmoney（首页展示）
- 包含6个经典游戏：Super Mario Bros、Pac-Man、Tetris、Space Invaders、Frogger、Snake

2. **配置网站信息**

编辑 `config.json` 文件，这是网站的全局配置文件：

```json
{
  "site_name": "Bloodmoney",
  "available_themes": [
    "default", "retro", "cyberpunk", "forest", "ocean", "battle",
    "dark", "battle-1", "ocean-1", "cyberpunk-1", "forest-1",
    "default-1", "default-2"
  ],
  "selected_theme": "default-2",
  "site_url": "https://bloodmoneygame.space",
  "build_settings": {
    "output_dir": "dist",
    "copy_assets": true,
    "minify_css": false,
    "minify_js": false
  },
  "seo": {
    "default_title": "Bloodmoney Game Online - Play Free Online Games",
    "default_description": "Play the best free online games at bloodmoneygame.space. Enjoy classic arcade games, puzzle games, action games and more!",
    "keywords": "online games, free games, arcade games, puzzle games, action games, browser games",
    "author": "bloodmoneygame Team",
    "language": "en",
    "canonical_base_url": "https://bloodmoneygame.space"
  },
  "contact": {
    "email": "<EMAIL>",
    "response_time": "24-48 hours"
  },
  "iframe_settings": {
    "default_size": "large",
    "sizes": {
      "small": { "width": 854, "height": 480, "description": "Standard size" },
      "large": { "width": 960, "height": 540, "description": "Large size" },
      "xlarge": { "width": 1280, "height": 720, "description": "Extra large size" }
    }
  },
  "display_settings": {
    "homepage": {
      "new_games_display": {
        "enabled": true,
        "count": 8,
        "selection_method": "configured",
        "selected_games": ["space-invaders", "frogger", "snake", "super-mario-bros"]
      },
      "popular_games_display": {
        "enabled": true,
        "count": 6,
        "columns": 1,
        "selection_method": "configured",
        "selected_games": ["super-mario-bros", "pac-man", "tetris"]
      }
    }
  },
  "sitemap": {
    "enabled": true,
    "base_url": "https://bloodmoneygame.space",
    "generate_index": true,
    "sitemaps": {
      "static": { "enabled": true },
      "popular": { "enabled": true },
      "new": { "enabled": true },
      "legal": { "enabled": true }
    }
  }
}
```

3. **配置游戏数据**

编辑 `games.json` 文件，管理所有游戏内容：

```json
[
  {
    "id": "bloodmoney",
    "name": "bloodmoney",
    "category": "popular",
    "thumbnail": "images/common/bloodmoney.png",
    "gameUrl": "https://html-classic.itch.zone/html/14646601/index.html",
    "feature_blocks": [
      {
        "type": "youtube",
        "videoId": "8tY9iRURyxo",
        "title": "Watch Gameplay"
      },
      {
        "type": "section",
        "title": "Welcome to the World of BLOODMONEY!",
        "content": "Step into the gritty underworld of BLOODMONEY, where every decision could be your last...",
        "format": "paragraph"
      }
    ],
    "meta": {
      "title": "Bloodmoney Game Online - Play Free",
      "description": "Play Bloodmoney game online for free. Experience the ultimate action-packed adventure."
    }
  },
  {
    "id": "super-mario-bros",
    "name": "Super Mario Bros",
    "category": "popular",
    "thumbnail": "images/popular_games_image/super-mario-bros.png",
    "gameUrl": "https://html5.gamemonetize.co/qotzmhhui1arsut4sg9zuhy7ualnvag4/",
    "feature_blocks": [
      {
        "type": "youtube",
        "videoId": "rLl9XBg7wSs",
        "title": "Watch Gameplay"
      },
      {
        "type": "section",
        "title": "What is Super Mario Bros",
        "content": "Super Mario Bros is a legendary platform adventure game...",
        "format": "paragraph"
      }
    ],
    "meta": {
      "title": "Super Mario Bros - Play Classic Nintendo Game Online",
      "description": "Play the classic Super Mario Bros game online for free."
    }
  }
]
```

4. **准备图片资源**

确保以下图片文件存在：

```
src/images/common/
├── logo.png          # 网站Logo（建议尺寸：200x50px，PNG格式，透明背景）
├── favicon.ico       # 网站图标（16x16px或32x32px，ICO格式）
└── bloodmoney.png    # 主游戏图片（当前项目的主要游戏）

src/images/popular_games_image/
├── super-mario-bros.png  # 超级马里奥缩略图
├── pac-man.png          # 吃豆人缩略图
├── tetris.png           # 俄罗斯方块缩略图
└── [其他游戏缩略图].png  # 热门游戏缩略图（建议尺寸：300x200px）

src/images/new_games_image/
├── space-invaders.png   # 太空入侵者缩略图
├── frogger.png         # 青蛙过河缩略图
├── snake.png           # 贪吃蛇缩略图
└── [其他游戏缩略图].png  # 新游戏缩略图（建议尺寸：300x200px）
```

5. **构建网站**
```bash
# 构建静态网站
npm run build

# 或者使用开发模式（构建后显示提示）
npm run dev

# 清理输出目录（如需要）
npm run clean
```

构建完成后，`dist/` 目录将包含完整的静态网站文件。

6. **本地预览**
构建完成后，可以通过以下方式预览：
- 直接打开 `dist/index.html` 文件
- 使用本地服务器（如 Live Server、Python HTTP Server等）

7. **部署网站**
将项目推送到Git仓库，然后连接到Cloudflare Pages或Vercel进行自动部署。

**💡 提示**：如果只需要部署静态文件（不包含源代码），可以使用 `.gitignore_game` 文件。详见下方的"仅部署模式"章节。

### Cloudflare Pages部署配置

项目已自动配置Cloudflare Pages支持：

1. **自动生成配置文件**：
   - `_redirects`: 404页面重定向配置
   - `404.html`: 自定义404错误页面

2. **部署步骤**：
   ```bash
   # 1. 构建项目
   npm run build

   # 2. 推送到Git仓库
   git add .
   git commit -m "Deploy to Cloudflare Pages"
   git push

   # 3. 在Cloudflare Pages中连接仓库
   # 4. 设置构建命令：npm run build
   # 5. 设置输出目录：dist
   ```

3. **自动功能**：
   - 404页面自动生效
   - Sitemap自动提交到搜索引擎
   - 主题样式完全兼容

## 📝 配置说明

### config.json 配置详解

#### 基本设置
- `site_name`: 网站名称，显示在页面标题、导航栏等位置
- `site_url`: 网站域名，用于SEO和链接生成
- `selected_theme`: 当前使用的主题，从available_themes中选择

#### 主题系统
支持13种内置主题：
- `default` / `default-1` / `default-2`: 默认蓝色主题系列
- `retro`: 复古风格主题
- `cyberpunk` / `cyberpunk-1`: 赛博朋克主题系列
- `forest` / `forest-1`: 森林绿色主题系列
- `ocean` / `ocean-1`: 海洋蓝色主题系列
- `battle` / `battle-1`: 战斗红色主题系列
- `dark`: 暗黑主题

#### iframe设置
- `default_size`: 默认iframe尺寸（small/large/xlarge）
- `sizes.small`: 小尺寸iframe（854x480px）
- `sizes.large`: 大尺寸iframe（960x540px）
- `sizes.xlarge`: 超大尺寸iframe（1280x720px）

#### 显示设置
- `new_games_display.enabled`: 是否启用新游戏功能（true/false）
- `new_games_display.count`: 首页新游戏显示数量
- `new_games_display.selection_method`: 游戏选择方式（configured/sequential/random）
- `new_games_display.selected_games`: 配置化选择的游戏ID列表
- `popular_games_display.enabled`: 是否启用热门游戏功能（true/false）
- `popular_games_display.count`: 首页热门游戏显示数量
- `popular_games_display.columns`: 热门游戏列数（1或2）
- `popular_games_display.selection_method`: 游戏选择方式（configured/sequential/random）
- `popular_games_display.selected_games`: 配置化选择的游戏ID列表

#### Sitemap设置
- `sitemap.enabled`: 是否启用sitemap生成（true/false）
- `sitemap.base_url`: sitemap中使用的基础URL
- `sitemap.generate_index`: 是否生成主sitemap索引文件（true/false）
- `sitemap.sitemaps.static.enabled`: 是否生成静态页面sitemap
- `sitemap.sitemaps.popular.enabled`: 是否生成热门游戏sitemap
- `sitemap.sitemaps.new.enabled`: 是否生成新游戏sitemap
- `sitemap.sitemaps.legal.enabled`: 是否生成法律页面sitemap

#### SEO设置
- `seo.default_title`: 默认页面标题
- `seo.default_description`: 默认页面描述
- `seo.keywords`: 网站关键词
- `seo.author`: 网站作者
- `seo.canonical_base_url`: 规范化URL基础地址

### games.json 数据结构

#### 特殊约定
- 数组第一个对象`[0]`：专门用于生成首页内容
- 数组从第二个对象`[1]`开始：列表游戏，会生成独立详情页

#### 游戏对象结构
```json
{
  "id": "unique-game-id",
  "name": "Game Name",
  "category": "popular|new",
  "thumbnail": "images/path/to/thumbnail.png",
  "gameUrl": "https://game-url.com",
  "feature_blocks": [
    {
      "type": "youtube",
      "videoId": "youtube-video-id",
      "title": "Video Title"
    },
    {
      "type": "section",
      "title": "Section Title",
      "content": "Section content",
      "format": "paragraph|list"
    }
  ],
  "meta": {
    "title": "SEO Title",
    "description": "SEO Description"
  }
}
```

#### 功能块类型
- `youtube`: YouTube视频嵌入
- `section`: 文本段落或列表
- `heading`: 标题
- `paragraph`: 段落
- `custom_html`: 自定义HTML

## ⚙️ 功能开关系统

### Popular Games 和 New Games 开关
通过配置文件可以独立控制Popular Games和New Games功能：

```json
{
  "display_settings": {
    "homepage": {
      "popular_games_display": {
        "enabled": true,  // 设置为false禁用Popular Games
        "count": 6,
        "selection_method": "configured"
      },
      "new_games_display": {
        "enabled": true,  // 设置为false禁用New Games
        "count": 8,
        "selection_method": "configured"
      }
    }
  }
}
```

### 功能开关影响范围
当设置为`false`时，系统会自动：

1. **页面生成**：
   - 跳过对应分类的游戏详情页生成
   - 跳过对应分类的列表页生成
   - 首页不显示对应分类的游戏列表

2. **Sitemap生成**：
   - 不生成对应分类的sitemap文件
   - 主sitemap.xml不包含对应分类的引用

3. **导航状态**：
   - 对应导航链接添加`nav-disabled`样式
   - 保持导航结构完整，但视觉上显示为禁用状态

4. **构建日志**：
   - 显示跳过的页面和sitemap信息
   - 提供清晰的构建反馈

## 🎨 主题系统

### 主题切换
在`config.json`中修改`selected_theme`字段即可切换主题：

```json
{
  "selected_theme": "default-2"
}
```

### 可用主题列表
项目包含13种精美主题：
- **default系列**: `default`, `default-1`, `default-2` - 经典蓝色主题
- **retro**: 复古橙黄色风格
- **cyberpunk系列**: `cyberpunk`, `cyberpunk-1` - 赛博朋克紫粉色
- **forest系列**: `forest`, `forest-1` - 自然绿色主题
- **ocean系列**: `ocean`, `ocean-1` - 海洋蓝色主题
- **battle系列**: `battle`, `battle-1` - 战斗红色主题
- **dark**: 暗黑主题

### 自定义主题
1. 在`src/css/themes/`目录下创建新的主题文件
2. 使用CSS变量定义主题颜色
3. 在`config.json`的`available_themes`中添加主题名称

### 主题变量系统
每个主题都使用完整的CSS变量系统（100+变量）：

```css
:root {
  /* 主要颜色变量 */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --bg-primary: #ffffff;
  --text-primary: #1e293b;

  /* 字体变量 */
  --font-family-primary: 'Fredoka', sans-serif;
  --font-size-base: 1rem;

  /* 间距和动画变量 */
  --spacing-md: 1rem;
  --transition-normal: 0.3s ease-in-out;
  /* 更多变量... */
}
```

## 🔧 构建系统

### 可用命令

```bash
npm run build    # 构建生产版本
npm run dev      # 开发模式构建（构建后显示提示）
npm run clean    # 清理输出目录（Windows优化）
npm test         # 运行测试（当前未配置）
```

### 构建特性
- **智能构建**：只生成启用功能的页面
- **主题应用**：自动应用选定主题
- **资源优化**：完整复制所有静态资源
- **搜索数据生成**：自动生成搜索所需的JavaScript数据
- **SEO文件生成**：自动生成Sitemap、robots.txt等
- **错误处理**：完善的错误处理和日志输出

### 构建过程详解

构建脚本 `build.js` 执行以下步骤：

1. **读取配置文件**：
   - 加载 `config.json` 获取网站配置
   - 加载 `games.json` 获取游戏数据
   - 验证配置有效性

2. **处理模板文件**：
   - 读取 `src/templates/` 下的HTML模板
   - 替换模板中的占位符（如 `{{SITE_NAME}}`、`{{GAMES_LIST}}` 等）
   - 根据配置生成动态内容
   - 应用主题样式

3. **生成游戏内容**：
   - 为每个游戏生成独立的详情页
   - 生成游戏卡片组件
   - 处理feature_blocks内容块（YouTube、段落、自定义HTML）
   - 根据功能开关决定生成哪些页面

4. **生成搜索数据**：
   - 自动生成 `games-data.js` 文件
   - 包含所有游戏的搜索必需信息
   - 优化搜索性能

5. **处理静态资源**：
   - 复制CSS文件到输出目录
   - 应用选定的主题样式
   - 复制JavaScript文件
   - 复制所有图片资源（保持目录结构）

6. **生成SEO文件**：
   - 生成智能Sitemap系统
   - 创建robots.txt文件
   - 生成404错误页面
   - 创建Cloudflare Pages重定向配置

7. **输出构建结果**：
   - 生成的文件输出到 `dist/` 目录
   - 创建完整的静态网站结构
   - 生成SEO友好的HTML文件
   - 显示详细的构建日志

### 构建后的目录结构

```
dist/
├── index.html                    # 主页
├── 404.html                      # 自定义404错误页面
├── sitemap.xml                   # 主sitemap索引文件
├── robots.txt                    # 搜索引擎爬虫配置
├── _redirects                    # Cloudflare Pages重定向配置
├── sitemaps/                     # Sitemap文件目录
│   ├── sitemap-static.xml        # 静态页面sitemap
│   ├── sitemap-popular.xml       # 热门游戏sitemap（可选）
│   ├── sitemap-new.xml           # 新游戏sitemap（可选）
│   └── sitemap-legal.xml         # 法律页面sitemap
├── popular_games/               # 热门游戏详情页目录（可选）
│   ├── super-mario-bros.html
│   ├── pac-man.html
│   └── tetris.html
├── new_games/                   # 新游戏详情页目录（可选）
│   ├── space-invaders.html
│   ├── frogger.html
│   └── snake.html
├── popular/                     # 热门游戏分类页（可选）
│   ├── index.html
│   ├── page-2.html
│   └── page-3.html
├── new/                         # 新游戏分类页（可选）
│   ├── index.html
│   ├── page-2.html
│   └── page-3.html
├── css/                         # 样式文件
│   ├── style.css
│   └── themes/
│       └── theme-[selected].css
├── js/                          # JavaScript文件
│   ├── main.js
│   └── games-data.js           # 自动生成的搜索数据
├── images/                      # 图片资源（完整复制）
│   ├── common/
│   ├── popular_games_image/
│   └── new_games_image/
└── legal_info/                  # 法律信息页面
    ├── About-Us.html
    ├── Contact-Us.html
    ├── DMCA.html
    └── Privacy-Policy.html
```

**注意**：标记为"（可选）"的目录和文件会根据配置中的功能开关决定是否生成。

## 💻 JavaScript功能

### 核心功能模块

#### 1. 移动端菜单系统
- 响应式导航菜单
- 汉堡包菜单动画
- 触摸友好的交互
- 自动关闭功能

#### 2. 智能游戏搜索功能
- 实时搜索过滤，模糊匹配算法
- 键盘导航支持（上下箭头、回车选择）
- 移动端搜索优化
- 防抖优化性能（300ms延迟）
- 相似度计算和结果排序
- 搜索结果高亮显示

#### 3. 游戏控制系统
- 游戏加载优化
- 全屏模式支持
- 移动端自动全屏
- iframe尺寸动态调整
- 游戏预览界面

#### 4. 游戏卡片交互
- 悬停效果和动画
- 点击反馈
- 懒加载支持
- 响应式布局

#### 5. 滚动效果和导航
- 返回顶部按钮
- Header阴影效果
- 平滑滚动
- 禁用导航状态处理

#### 6. Feature折叠功能
- 动态内容块折叠/展开
- 平滑动画过渡
- 移动端优化

#### 7. 性能优化
- 图片懒加载
- 防抖函数
- 事件委托
- 模块化代码组织

### 搜索功能详解

搜索功能基于构建时生成的`games-data.js`文件：

```javascript
// 自动生成的搜索数据
window.GAMES_DATA = [
  {
    "id": "super-mario-bros",
    "name": "Super Mario Bros",
    "category": "popular",
    "thumbnail": "images/popular_games_image/super-mario-bros.png",
    "url": "popular_games/super-mario-bros.html"
  },
  {
    "id": "space-invaders",
    "name": "Space Invaders",
    "category": "new",
    "thumbnail": "images/new_games_image/space-invaders.png",
    "url": "new_games/space-invaders.html"
  }
  // 更多游戏数据...
];
```

#### 搜索算法特性
- **模糊匹配**：支持部分字符匹配
- **相似度计算**：基于字符串相似度排序结果
- **实时搜索**：输入时即时显示结果
- **键盘导航**：支持方向键和回车键操作
- **防抖优化**：避免频繁搜索请求

## 🎯 组件系统

### 游戏卡片组件 (game-card.html)

用于生成游戏列表的可复用组件：

```html
<a href="{{GAME_CATEGORY}}_games/{{GAME_ID}}.html" class="game-card">
    <img src="{{GAME_THUMBNAIL}}" alt="{{GAME_NAME}}" class="game-thumbnail" loading="lazy">
    <div class="game-info">
        <h3 class="game-title">{{GAME_NAME}}</h3>
    </div>
</a>
```

### 功能块组件 (feature-blocks.html)

支持多种内容块类型：

#### YouTube视频块
```html
<div class="feature-block youtube-block">
    <div class="youtube-embed">
        <iframe src="https://www.youtube.com/embed/{{VIDEO_ID}}"
                title="{{VIDEO_TITLE}}" frameborder="0" allowfullscreen>
        </iframe>
    </div>
</div>
```

#### 文本段落块
```html
<div class="feature-block section-block">
    <h2>{{SECTION_TITLE}}</h2>
    <p>{{SECTION_CONTENT}}</p>
</div>
```

#### 自定义HTML块
```html
<div class="feature-block custom-html-block">
    {{CUSTOM_HTML_CONTENT}}
</div>
```

## 📦 部署

### Cloudflare Pages 部署

1. **连接GitHub仓库**
   - 登录Cloudflare Pages
   - 选择"连接到Git"
   - 选择你的GitHub仓库

2. **配置构建设置**
   ```
   构建命令: node build.js
   输出目录: dist
   Node.js版本: 14.x 或更高
   ```

3. **环境变量**（可选）
   ```
   NODE_VERSION=14.x
   ```

4. **自动部署**
   - 每次推送到主分支会自动触发重新部署
   - 支持预览分支部署

### Vercel 部署

1. **连接项目**
   ```bash
   npm i -g vercel
   vercel
   ```

2. **配置文件** (vercel.json)
   ```json
   {
     "buildCommand": "node build.js",
     "outputDirectory": "dist",
     "installCommand": "npm install"
   }
   ```

### 本地构建部署

如果需要手动部署到其他平台：

```bash
# 本地构建
npm run build

# 将 dist/ 目录内容上传到你的Web服务器
```

### 🚀 仅部署模式 (.gitignore_game)

项目包含一个特殊的 `.gitignore_game` 文件，专门用于**只上传构建后的静态文件**到 GitHub，适用于部署到 GitHub Pages 或其他静态托管服务。

#### 使用场景
- 部署新游戏网站时，只需要上传 `dist` 目录中的文件
- 保持部署仓库干净，不包含源代码和构建脚本
- 适用于 GitHub Pages、Netlify、Vercel 等静态托管平台

#### 使用步骤

1. **构建项目**：
   ```bash
   npm run build
   ```

2. **切换到部署模式**：
   ```bash
   # 备份原始 .gitignore（可选）
   cp .gitignore .gitignore_backup

   # 使用部署配置覆盖 .gitignore
   cp .gitignore_game .gitignore
   ```

3. **提交并推送**：
   ```bash
   git add .
   git commit -m "Deploy game site - dist only"
   git push
   ```

4. **恢复开发模式**（可选）：
   ```bash
   # 恢复原始 .gitignore
   cp .gitignore_backup .gitignore
   ```

#### 部署配置说明

`.gitignore_game` 文件的特点：
- ✅ **只上传 dist 目录**：包含所有构建后的静态文件
- ❌ **忽略源代码**：src/、build.js、config.json、games.json 等
- ❌ **忽略开发文件**：package.json、node_modules/ 等
- ✅ **保留必要文件**：.gitignore 本身

#### 确保上传的文件类型
- HTML 文件（index.html、游戏详情页等）
- CSS 文件（样式和主题文件）
- JavaScript 文件（交互功能和搜索数据）
- 图片资源（Logo、游戏缩略图等）
- SEO 文件（sitemap.xml、robots.txt）
- 法律信息页面

这种方式特别适合：
- 🎮 **游戏网站部署**：快速部署到静态托管平台
- 🔒 **源码保护**：不暴露构建脚本和配置文件
- ⚡ **轻量仓库**：只包含最终产物，仓库体积小
- 🚀 **快速部署**：无需服务器端构建，直接使用静态文件

## 🛠️ 开发指南

### 修改模板

#### 主页模板 (_main-layout-template.html)
- 包含完整的主页布局
- 支持iframe游戏区域
- 包含Popular Games和New Games区域
- 支持动态内容块

#### 游戏详情页模板 (game-detail-template.html)
- 游戏详情页布局
- 支持feature_blocks渲染
- 包含侧边栏游戏推荐

#### 列表页模板 (_list-layout-template.html)
- 分类页面布局
- 支持分页功能
- 响应式游戏网格

#### 分页模板 (_list-page-template.html)
- 专门用于分页列表
- 包含分页导航
- 支持面包屑导航

### 添加新组件

1. 在 `src/components/` 创建组件HTML文件
2. 在 `build.js` 中添加组件处理逻辑
3. 在模板中使用占位符引用组件

### 自定义样式

#### 主样式文件 (style.css)
- 使用CSS变量体系
- 响应式设计
- 模块化样式组织

#### 主题样式
- 每个主题都是独立的CSS文件
- 通过CSS变量覆盖实现主题切换
- 支持自定义主题开发

### 图片资源管理

#### 图片优化建议
- **Logo**: 200x50px，PNG格式，透明背景
- **Favicon**: 16x16px或32x32px，ICO格式
- **首页游戏图片**: 800x450px，JPEG格式
- **游戏缩略图**: 300x200px，PNG或JPEG格式

#### 图片命名规范
- 使用小写字母和连字符
- 避免特殊字符和空格
- 保持文件名与游戏ID一致

## 📋 维护指南

### 日常维护流程

#### 添加新游戏
1. 准备游戏缩略图，放入对应目录
2. 在`games.json`中添加游戏数据
3. 运行`npm run build`重新构建
4. 提交代码并推送到仓库

#### 更新游戏信息
1. 修改`games.json`中的游戏数据
2. 重新构建网站
3. 部署更新

#### 切换主题
1. 修改`config.json`中的`selected_theme`
2. 重新构建网站
3. 部署更新

#### 更新网站配置
1. 修改`config.json`中的相关设置
2. 重新构建网站
3. 部署更新

### 故障排除

#### 常见问题

**Q: 构建失败怎么办？**
A: 检查Node.js版本是否≥14.0.0，确保`config.json`和`games.json`格式正确。

**Q: 图片无法显示？**
A: 检查图片文件路径是否正确，确保文件名与配置中的路径完全一致。

**Q: 主题切换无效？**
A: 确保主题名称在`available_themes`中存在，重新运行构建命令。

**Q: 游戏无法加载？**
A: 检查`gameUrl`是否正确，确保外部链接可访问。

**Q: 搜索功能不工作？**
A: 确保`games-data.js`文件已正确生成，检查JavaScript控制台是否有错误。

### 性能优化

#### 图片优化
- 使用WebP格式（如果浏览器支持）
- 启用图片懒加载
- 压缩图片文件大小

#### CSS优化
- 在生产环境启用CSS压缩
- 使用CSS变量减少重复代码
- 优化CSS选择器性能

#### JavaScript优化
- 启用JavaScript压缩
- 使用防抖优化搜索功能
- 实现代码分割（如需要）

## 📊 SEO优化

### 元数据管理
- 每个页面都有独立的title和description
- 支持Open Graph和Twitter Cards
- 自动生成canonical URL

### 智能Sitemap系统
- **自动生成分类sitemap**：根据游戏类型自动分类
- **功能开关控制**：Popular/New Games禁用时，对应sitemap不生成
- **文件组织结构**：
  ```
  dist/
  ├── sitemap.xml              # 主sitemap索引文件
  ├── robots.txt               # 搜索引擎配置
  └── sitemaps/                # sitemap文件目录
      ├── sitemap-static.xml   # 静态页面（首页、分类页）
      ├── sitemap-popular.xml  # 热门游戏页面
      ├── sitemap-new.xml      # 新游戏页面
      └── sitemap-legal.xml    # 法律信息页面
  ```
- **智能更新**：只包含实际生成的页面
- **搜索引擎友好**：符合sitemap.xml标准格式

### 404错误页面
- **自定义404页面**：保持网站主题一致性
- **用户友好**：提供返回首页和浏览游戏的快捷链接
- **SEO优化**：正确的HTTP 404状态码
- **自动配置**：Cloudflare Pages自动识别

### 结构化数据
- 游戏页面包含结构化数据
- 支持搜索引擎更好地理解内容
- 提高搜索结果展示效果

### 性能优化
- 静态文件部署，加载速度快
- 图片懒加载，减少初始加载时间
- CSS和JavaScript优化

## 🔒 法律信息页面

项目包含完整的法律信息页面：

- **About Us** (`src/legal_info/About-Us.html`): 关于我们页面
- **Contact Us** (`src/legal_info/Contact-Us.html`): 联系我们页面
- **DMCA** (`src/legal_info/DMCA.html`): 版权声明页面
- **Privacy Policy** (`src/legal_info/Privacy-Policy.html`): 隐私政策页面

这些页面会自动使用`config.json`中的配置信息，包括网站名称、联系邮箱等。

## 🔧 故障排除

### 常见问题

#### 1. 404页面在Cloudflare Pages上不显示
**解决方案**：
- 确保`_redirects`文件已生成在dist目录
- 检查Cloudflare Pages的部署日志
- 验证404.html文件存在于根目录

#### 2. Sitemap文件没有生成
**解决方案**：
- 检查`config.json`中的`sitemap.enabled`设置
- 确认对应功能开关（Popular/New Games）已启用
- 查看构建日志中的sitemap生成信息

#### 3. 游戏页面没有生成
**解决方案**：
- 检查`games.json`中的游戏数据格式
- 确认`display_settings`中对应功能已启用
- 验证游戏ID的唯一性

#### 4. 主题样式没有应用
**解决方案**：
- 检查`config.json`中的`selected_theme`设置
- 确认主题文件存在于`src/css/themes/`目录
- 重新构建项目

#### 5. 搜索功能不工作
**解决方案**：
- 确认`games-data.js`文件已生成
- 检查浏览器控制台是否有JavaScript错误
- 验证游戏数据格式正确

### 构建日志解读
构建过程会显示详细的日志信息：
- `✅ Generated`: 成功生成的文件
- `⏭️ Skipped`: 因功能开关跳过的文件
- `📄 disabled`: 功能被禁用的提示
- `🎯 selection`: 游戏选择结果统计

## 📊 项目统计

### 代码规模
- **总代码行数**: 4000+ 行
- **主样式文件**: 2400+ 行 CSS（完整的变量体系）
- **JavaScript功能**: 600+ 行（模块化设计）
- **HTML模板**: 5个核心模板文件
- **主题文件**: 13个主题，每个100-200行CSS
- **配置文件**: 详细的JSON配置系统

### 功能特性
- ✅ **13种主题**：完整的主题切换系统
- ✅ **智能搜索**：模糊匹配 + 键盘导航
- ✅ **响应式设计**：移动优先，完美适配所有设备
- ✅ **SEO优化**：完整的元数据和Sitemap系统
- ✅ **游戏管理**：灵活的JSON数据管理
- ✅ **功能开关**：可独立控制各功能模块
- ✅ **自动化构建**：一键生成静态网站
- ✅ **部署就绪**：支持Cloudflare Pages等平台
- ✅ **仅部署模式**：`.gitignore_game` 支持只上传构建文件

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

### 贡献指南
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 支持

如果你在使用过程中遇到问题，可以：

1. 查看本文档的故障排除部分
2. 提交GitHub Issue
3. 发送邮件到 <EMAIL>

## 🏆 致谢

感谢所有为这个项目做出贡献的开发者和设计师！

---

**Bloodmoney Game Portal** - 让游戏网站开发变得简单高效！ 🎮✨

*最后更新：2025年8月22日*
