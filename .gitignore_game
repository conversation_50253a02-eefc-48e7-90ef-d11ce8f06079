# .gitignore_game - 部署模式配置
# 此文件用于部署新游戏网站时，只上传 dist 文件夹中的内容到仓库根目录
# 使用方法：
# 1. 先将 dist 目录中的所有文件移动到项目根目录
# 2. 将此文件内容覆盖到 .gitignore 文件中
# 3. 这样仓库中只包含构建后的静态文件，不包含 dist 文件夹本身

# ==========================================
# 忽略开发相关的所有文件和文件夹
# ==========================================

# 忽略源代码目录
src/

# 忽略构建和配置文件
build.js
config.json
games.json
package.json
package-lock.json
node_modules/

# 忽略文档文件
README.md
PRD.md
TODOLIST.md
BUILD_PLAN.md

# 忽略原始的 .gitignore 文件（保留当前的部署版本）
.gitignore_original

# 忽略其他开发文件
.gitignore_game
.vscode/
.idea/
*.log
*.tmp

# ==========================================
# 允许上传的文件（构建后的静态文件）
# ==========================================

# 允许所有 HTML 文件
!*.html

# 允许 CSS 和 JS 目录及文件
!css/
!css/**
!js/
!js/**

# 允许图片目录及文件
!images/
!images/**

# 允许 SEO 相关文件
!sitemap.xml
!robots.txt
!sitemaps/
!sitemaps/**

# 允许法律信息页面
!legal_info/
!legal_info/**

# 允许游戏页面目录
!popular/
!popular/**
!new/
!new/**
!popular_games/
!popular_games/**
!new_games/
!new_games/**

# 允许其他静态资源
!*.ico
!*.png
!*.jpg
!*.jpeg
!*.gif
!*.svg
!*.webp
!*.json
!*.xml
!*.txt
!_redirects

# ==========================================
# 说明注释
# ==========================================

# 此配置确保：
# 1. 只有构建后的静态文件会被上传到 GitHub 仓库根目录
# 2. 所有源代码、配置文件、构建脚本等都被忽略
# 3. 适用于部署静态网站到 GitHub Pages 或其他静态托管服务
# 4. 保持仓库干净，只包含最终的构建产物，不包含 dist 文件夹本身

# 使用步骤：
# 1. 本地运行 npm run build 生成 dist 目录
# 2. 将 dist 目录中的所有文件移动到项目根目录：
#    - Windows: robocopy dist . /E && rmdir /s /q dist
#    - Linux/Mac: cp -r dist/* . && rm -rf dist
# 3. 备份原始 .gitignore: cp .gitignore .gitignore_original
# 4. 将此文件内容复制到 .gitignore 文件中: cp .gitignore_game .gitignore
# 5. git add . && git commit -m "Deploy game site - static files only"
# 6. git push 只会上传构建后的静态文件到仓库根目录

# 注意：这种方式适合专门用于部署的仓库，不适合开发仓库
