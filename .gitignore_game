# .gitignore_game - 部署模式配置
# 此文件用于部署新游戏网站时，只上传 dist 文件夹中的内容
# 使用方法：将此文件内容覆盖到 .gitignore 文件中

# ==========================================
# 忽略所有文件和文件夹（除了 dist 目录）
# ==========================================

# 忽略根目录下的所有文件
/*

# 忽略所有隐藏文件和文件夹
.*

# ==========================================
# 例外：允许上传的文件和文件夹
# ==========================================

# 允许 dist 目录及其所有内容
!dist/
!dist/**

# 允许必要的 Git 配置文件
!.gitignore

# 允许基本的项目说明文件（可选，根据需要调整）
# !README.md
# !LICENSE

# ==========================================
# 确保 dist 目录内容完全上传
# ==========================================

# 确保 dist 目录下的所有文件类型都被包含
!dist/*.html
!dist/*.css
!dist/*.js
!dist/*.json
!dist/*.xml
!dist/*.txt
!dist/*.ico
!dist/*.png
!dist/*.jpg
!dist/*.jpeg
!dist/*.gif
!dist/*.svg
!dist/*.webp

# 确保 dist 子目录都被包含
!dist/css/
!dist/css/**
!dist/js/
!dist/js/**
!dist/images/
!dist/images/**
!dist/sitemaps/
!dist/sitemaps/**
!dist/legal_info/
!dist/legal_info/**
!dist/popular/
!dist/popular/**
!dist/new/
!dist/new/**
!dist/popular_games/
!dist/popular_games/**
!dist/new_games/
!dist/new_games/**

# ==========================================
# 说明注释
# ==========================================

# 此配置确保：
# 1. 只有 dist 目录中的文件会被上传到 GitHub
# 2. 所有源代码、配置文件、构建脚本等都被忽略
# 3. 适用于部署静态网站到 GitHub Pages 或其他静态托管服务
# 4. 保持仓库干净，只包含最终的构建产物

# 使用步骤：
# 1. 本地运行 npm run build 生成 dist 目录
# 2. 将此文件内容复制到 .gitignore 文件中
# 3. git add . && git commit -m "Deploy game site"
# 4. git push 只会上传 dist 目录内容
