# .gitignore_game - 部署模式配置
# 此文件用于部署新游戏网站时，只上传 dist 文件夹中的内容
# 使用方法：将此文件内容覆盖到 .gitignore 文件中

# ==========================================
# 忽略所有文件和文件夹（除了 dist 目录）
# ==========================================

# 忽略根目录下的所有文件和文件夹
/*

# 忽略所有隐藏文件和文件夹
.*

# ==========================================
# 例外：允许上传的文件和文件夹
# ==========================================

# 允许 dist 目录及其所有内容
!dist/
!dist/**

# 允许 .gitignore 文件本身
!.gitignore

# ==========================================
# 说明注释
# ==========================================

# 此配置确保：
# 1. 只有 dist 目录会被上传到 GitHub
# 2. 所有源代码、配置文件、构建脚本等都被忽略
# 3. 适用于部署静态网站到 GitHub Pages 或其他静态托管服务
# 4. 保持仓库干净，只包含最终的构建产物

# 使用步骤：
# 1. 本地运行 npm run build 生成 dist 目录
# 2. 备份原始 .gitignore: cp .gitignore .gitignore_backup
# 3. 将此文件内容复制到 .gitignore 文件中: cp .gitignore_game .gitignore
# 4. git add . && git commit -m "Deploy game site - dist only"
# 5. git push 只会上传 dist 目录和 .gitignore 文件

# 最终效果：GitHub 仓库中只有 dist 文件夹和 .gitignore 文件
# 注意：这种方式适合专门用于部署的仓库或分支
