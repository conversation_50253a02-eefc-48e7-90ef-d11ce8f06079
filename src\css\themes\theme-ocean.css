/* GamePort - Ocean Theme (深海探险 - 青蓝色系) */
/* 深蓝海洋风格，神秘深海探索主题 */

:root {
  /* 主要颜色变量 - 青蓝色系 */
  --primary-color: #0891b2;
  --primary-color-2: #0891b2;
  --primary-hover: #0e7490;
  --primary-light: #cffafe;
  --secondary-color: #155e75;
  --accent-color: #06b6d4;
  
  /* 背景颜色 */
  --bg-primary: #f0f9ff;
  --bg-secondary: #e0f2fe;
  --bg-tertiary: #bae6fd;
  --bg-quaternary: #f8f9fa;
  --bg-info: #bae6fd;
  --bg-dark: #164e63;
  
  /* Header专用背景色 - 比主背景更深 */
  --header-bg-color: #e0f2fe;
  
  /* 文字颜色 */
  --text-primary: #164e63;
  --text-secondary: #0e7490;
  --text-light: #0891b2;
  --text-white: #ffffff;
  
  /* 辅助颜色变量 */
  --border-color: #bae6fd;
  --border-hover: #7dd3fc;
  --shadow-color: rgba(8, 145, 178, 0.15);
  --shadow-hover: rgba(8, 145, 178, 0.25);
  
  /* 状态颜色 */
  --success-color: #059669;
  --warning-color: #f59e0b;
  --error-color: #dc2626;
  --info-color: #0891b2;
  
  /* 按钮颜色状态 */
  --btn-primary-bg: #0891b2;
  --btn-primary-hover: #0e7490;
  --btn-primary-active: #155e75;
  --btn-secondary-bg: #06b6d4;
  --btn-secondary-hover: #0891b2;
  
  /* 链接颜色 */
  --link-color: #0891b2;
  --link-hover: #0e7490;
  --link-visited: #155e75;
  
  /* 特殊效果颜色 */
  --gradient-primary: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
  --gradient-secondary: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}
